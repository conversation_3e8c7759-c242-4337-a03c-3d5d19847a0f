resource "aws_cloudfront_origin_access_identity" "origin_access_identity" {
  comment = "access-identity-${var.s3-bucket-bucket}.s3.amazonaws.com"
}

resource "aws_cloudfront_distribution" "s3_distribution" {
  origin {
    origin_id   = "S3-${var.s3-bucket-bucket}"
    origin_path = "/client/auction"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.origin_access_identity.cloudfront_access_identity_path
    }

    domain_name = var.bucket_regional_domain_name
  }

  origin {
    origin_id = "S3-${var.s3-bucket-bucket}-static"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.origin_access_identity.cloudfront_access_identity_path
    }

    domain_name = var.bucket_regional_domain_name
  }

  origin {
    origin_id = "S3-${var.s3-bucket-bucket}-public"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.origin_access_identity.cloudfront_access_identity_path
    }

    domain_name = var.bucket_regional_domain_name
  }

  origin {
    origin_id = "S3-${var.s3-bucket-bucket}-constant"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.origin_access_identity.cloudfront_access_identity_path
    }

    domain_name = var.bucket_regional_domain_name
  }

  origin {
    origin_id = "S3-${var.s3-bucket-bucket}-item-ancillary"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.origin_access_identity.cloudfront_access_identity_path
    }

    domain_name = var.bucket_regional_domain_name
  }

  origin {
    domain_name = replace(var.api_gateway_deployment_invoke_url, "/^https?://([^/]*).*/", "$1")
    origin_id   = var.aws_api_gateway_rest_api_name

    custom_origin_config {
      http_port                = 80
      https_port               = 443
      origin_keepalive_timeout = 5
      origin_protocol_policy   = "match-viewer"
      origin_read_timeout      = 60
      origin_ssl_protocols     = ["TLSv1", "TLSv1.1", "TLSv1.2"]
    }
  }

  aliases             = var.domain_name != "" ? [var.domain_name] : []
  enabled             = true
  is_ipv6_enabled     = false
  comment             = "${var.environment}-${var.project_name}-auction-side-cloudfront"
  default_root_object = "index.html"

custom_error_response {
  error_caching_min_ttl = 0
  error_code            = 403
  response_code         = 403
  response_page_path    = "/index.html"
}

custom_error_response {
  error_caching_min_ttl = 0
  error_code            = 404
  response_code         = 404
  response_page_path    = "/index.html"
}
  custom_error_response {
    error_caching_min_ttl = 0
    error_code            = 405
    response_code         = 405
    response_page_path    = "/blocked_request.html"
  }

  default_cache_behavior {
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-${var.s3-bucket-bucket}"

    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 86400
    max_ttl                = 31536000
    compress               = true

    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = module.origin-response.lambda_function_qualified_arn
    }

    dynamic "lambda_function_association" {
      for_each = var.basic_auth_enable == false ? [] : [0]
      content {
        event_type   = "viewer-request"
        include_body = false
        lambda_arn   = module.viewer-request.lambda_function_qualified_arn
      }
    }
  }

  ordered_cache_behavior {
    path_pattern     = "/index.html"
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-${var.s3-bucket-bucket}"

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }

    min_ttl                = 0
    default_ttl            = 0
    max_ttl                = 0
    compress               = true
    viewer_protocol_policy = "redirect-to-https"

    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = module.origin-response.lambda_function_qualified_arn
    }
  }

  ordered_cache_behavior {
    path_pattern     = "/public/*"
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-${var.s3-bucket-bucket}-public"

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }

    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = module.origin-response.lambda_function_qualified_arn
    }

    min_ttl                = 0
    default_ttl            = 0
    max_ttl                = 0
    compress               = true
    viewer_protocol_policy = "redirect-to-https"
  }

  ordered_cache_behavior {
    path_pattern     = "/static/*"
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-${var.s3-bucket-bucket}-static"

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }

    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = module.origin-response.lambda_function_qualified_arn
    }

    min_ttl                = 0
    default_ttl            = 0
    max_ttl                = 0
    compress               = true
    viewer_protocol_policy = "redirect-to-https"
  }

  ordered_cache_behavior {
    path_pattern     = "/constant/*"
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-${var.s3-bucket-bucket}-constant"

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }

    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = module.origin-response.lambda_function_qualified_arn
    }

    min_ttl                = 0
    default_ttl            = 0
    max_ttl                = 0
    compress               = true
    viewer_protocol_policy = "redirect-to-https"
  }

  ordered_cache_behavior {
    path_pattern     = "/item-ancillary/*"
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-${var.s3-bucket-bucket}-item-ancillary"

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }

    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = module.origin-response.lambda_function_qualified_arn
    }

    min_ttl                = 0
    default_ttl            = 86400
    max_ttl                = 31536000
    compress               = true
    viewer_protocol_policy = "redirect-to-https"
  }

  ordered_cache_behavior {
    path_pattern     = "/api/*"
    allowed_methods  = ["GET", "HEAD", "OPTIONS", "POST", "PUT", "PATCH", "DELETE"]
    cached_methods   = ["HEAD", "GET"]
    target_origin_id = var.aws_api_gateway_rest_api_name

    forwarded_values {
      headers      = ["Accept", "Authorization", "Origin", "User-Agent"]
      query_string = true
      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "redirect-to-https"
  }

  price_class = "PriceClass_200"

  restrictions {
    geo_restriction {
      restriction_type = "none"
      locations        = []
    }
  }

  tags = {
    Environment = var.environment
  }

  # Production SSL configuration
  dynamic "viewer_certificate" {
    for_each = var.domain_name != "" ? [1] : []
    content {
      acm_certificate_arn      = module.acm.certificate_arn
      ssl_support_method       = "sni-only"
      minimum_protocol_version = "TLSv1.2_2021"
    }
  }

  # Non-production SSL configuration
  dynamic "viewer_certificate" {
    for_each = var.domain_name != "" ? [] : [1]
    content {
      cloudfront_default_certificate = true
      minimum_protocol_version = "TLSv1.2_2021"
    }
  }

  web_acl_id = var.environment == "stage_ok" ? "" : aws_wafv2_web_acl.web_auction_acl[0].arn

  depends_on = [module.acm]

  lifecycle {
    # ignore_changes = all
  }

}

module "origin-response" {
  source       = "../../../modules/origin-response"
  project_name = var.project_name
  environment  = var.environment
  profile_name = var.profile_name
}

module "viewer-request" {
  source       = "../../../modules/basic-auth-lambda"
  project_name = var.project_name
  environment  = var.environment
  profile_name = var.profile_name
  resource_folder_name = "auction-side"
}
