const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool();

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);

  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log('create-notice');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      // Validation
      const validateResult = [];

      const notice = params.data;
      if (typeof notice === 'undefined' || notice === null) {
        validateResult.push(Define.MESSAGE.E000109);
      }
      // Start date empty
      if (
        typeof notice.start_datetime === 'undefined' ||
        notice.start_datetime === null ||
        notice.start_datetime === ''
      ) {
        validateResult.push(Define.MESSAGE.E000115);
      }
      // End date empty
      if (
        typeof notice.end_datetime === 'undefined' ||
        notice.end_datetime === null ||
        notice.end_datetime === ''
      ) {
        validateResult.push(Define.MESSAGE.E000116);
      }
      // Start date invalid
      let tmpDate = new Date(notice.start_datetime);
      console.log(`tmpDate = ${tmpDate}`);
      if (!(tmpDate instanceof Date && !isNaN(tmpDate))) {
        validateResult.push(Define.MESSAGE.E000117);
      }
      // End date invalid
      tmpDate = new Date(notice.end_datetime);
      console.log(`tmpDate = ${tmpDate}`);
      if (!(tmpDate instanceof Date && !isNaN(tmpDate))) {
        validateResult.push(Define.MESSAGE.E000118);
      }
      // 開始日の大小チェック
      const tmpStDate = new Date(notice.start_datetime);
      const tmpEnDate = new Date(notice.end_datetime);
      if (tmpStDate.getTime() > tmpEnDate.getTime()) {
        console.log('開始日: From > To');
        validateResult.push(Define.MESSAGE.E000123);
      }

      // 日付が1年以内かのチェック
      const today = new Date(Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000);
      const maxDate = new Date(today);
      maxDate.setDate(today.getDate() + 364);
      maxDate.setHours(0, 0, 0, 0); // 時間をリセットして日付のみ取得
      console.log(`maxDate = ${maxDate}`)
      // 開始日のチェック
      const startDateTime = notice.start_datetime.split(' ');
      const startDateFormatted = startDateTime[0].replace(/\//g, '-');  // 日付のフォーマットをYYYY-MM-DDに変換
      const startDate = new Date(`${startDateFormatted}T00:00:00`);
      const startDateDiff = maxDate - startDate;
      if (startDateDiff < 0) {
        console.log('開始日: 1年以内ではない');
        validateResult.push(Define.MESSAGE.E000184);
      }
      // 終了日のチェック
      const endDateTime = notice.end_datetime.split(' ');
      const endDateFormatted = endDateTime[0].replace(/\//g, '-');  // 日付のフォーマットをYYYY-MM-DDに変換
      const endDate = new Date(`${endDateFormatted}T00:00:00`);
      const endDateDiff = maxDate - endDate;
      if (endDateDiff < 0) {
        console.log('終了日: 1年以内ではない');
        validateResult.push(Define.MESSAGE.E000185);
      }

      // Get language name list
      const languages = notice.language_name;
      if (
        typeof languages === 'undefined' ||
        languages === null ||
        languages.length <= 0
      ) {
        validateResult.push(Define.MESSAGE.E000110);
      }

      // Title empty check
      let isTitleEmpty = true;
      let errorTxt = '';
      for (let i = 0; i < languages.length; i++) {
        if (
          typeof notice.title[i] !== 'undefined' &&
          notice.title[i] !== null &&
          notice.title[i].trim().length > 0
        ) {
          isTitleEmpty = false;
        } else {
          if (errorTxt.length > 0) {
            errorTxt += ', ';
          }
          errorTxt += `${languages[i]}のタイトル`;
        }
      }
      if (isTitleEmpty || errorTxt.length > 0) {
        if (languages.length > 1) {
          validateResult.push(Common.format(Define.MESSAGE.E000119, [errorTxt,]));
        } else {
          validateResult.push(Define.MESSAGE.E000180);
        }
      }

      // Body empty check
      let isBodyEmpty = true;
      errorTxt = '';
      for (let i = 0; i < languages.length; i++) {
        if (
          typeof notice.body[i] !== 'undefined' &&
          notice.body[i] !== null &&
          notice.body[i].trim().length > 0
        ) {
          isBodyEmpty = false;
        } else {
          if (errorTxt.length > 0) {
            errorTxt += ', ';
          }
          errorTxt += `${languages[i]}の本文`;
        }
      }
      if (isBodyEmpty || errorTxt.length > 0) {
        if (languages.length > 1) {
          validateResult.push(Common.format(Define.MESSAGE.E000119, [errorTxt,]));
        } else {
          validateResult.push(Define.MESSAGE.E000181);
        }
      }

      // Validation Fail
      if (validateResult.length > 0) {
        const error = {
          status: 400,
          message: validateResult,
        };
        return Promise.reject(error);
      }

      // Validation successful
      if (params.validation_mode === true) {
        const response = {
          status: 200,
          message: '',
        };
        return Promise.reject(response);
      } else {
        return Promise.resolve();
      }
    })
    .then(() => {
      // Get notice_no
      const admin_no = Base.extractAdminNo(e);
      console.log(`admin_no = ${admin_no}`);
      const tenant_no = Base.extractTenantId(e);
      console.log(`tenant_no = ${tenant_no}`);

      const notice = params.data;
      let notice_no = 0;
      if (
        typeof notice.notice_no !== 'undefined' &&
        notice.notice_no !== null
      ) {
        notice_no = notice.notice_no;
      }

      // Get language list
      const languages = notice.language_code;
      // Start insert data
      return new Promise((resolve, reject) => {
        insertAllNotice(
          0,
          languages,
          notice,
          notice_no,
          admin_no,
          tenant_no,
          pool,
          res_notice_no => {
            if (res_notice_no === 0) {
              // Failed
              const response = {
                status: 400,
                message: Define.MESSAGE.E000111,
              };
              console.log('error', JSON.stringify(response));
              return reject(response);
            }
            // Successful
            return resolve(res_notice_no);
          }
        );
      });
    })
    .then(notice => {
      const response = {
        data: notice,
      };

      return Base.createSuccessResponse(cb, response);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};

const insertAllNotice = function (
  count,
  languages,
  notice,
  res_notice_no,
  admin_no,
  tenant_no,
  pool,
  callback
) {
  // Check max length
  if (count >= languages.length) {
    callback(res_notice_no);
    return;
  }

  // Res_notice_no=0 → DB update failed
  // When count=0 skip
  if (count > 0 && res_notice_no === 0) {
    callback(res_notice_no);
    return;
  }

  // Insert notice
  const sql = Define.QUERY.INSERT_OR_UPDATE_NOTICE_FUNCTION;
  const sql_params = [
    res_notice_no,
    tenant_no,
    notice.display_code,
    languages[count],
    notice.start_datetime,
    notice.end_datetime,
    notice.title[count],
    notice.title1[count],
    notice.sub_title[count],
    notice.body[count],
    notice.link_url[count],
    notice.file[count],
    admin_no,
    admin_no,
  ];

  console.log(`sql = ${JSON.stringify(sql)}`);
  console.log(`sql_params = ${JSON.stringify(sql_params)}`);

  pool
    .rlsQuery(tenant_no,sql, sql_params)
    .then(result => {
      console.log('result = ', JSON.stringify(result));
      // [{"f_insert_or_update_notice":215}]
      let retId = result[0].f_insert_or_update_notice;
      if (typeof retId === 'undefined' || retId === null) {
        retId = 0;
      }
      return insertAllNotice(
        count + 1,
        languages,
        notice,
        retId,
        admin_no,
        tenant_no,
        pool,
        callback
      );
    })
    .catch(error => {
      console.log('error', error);
      return insertAllNotice(
        count + 1,
        languages,
        notice,
        0,
        admin_no,
        tenant_no,
        pool,
        callback
      );
    });
};
