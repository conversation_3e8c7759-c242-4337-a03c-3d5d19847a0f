import useSearchResultState from '@/composables/state/useSearchResultState'
import useApi from '@/composables/useApi'
import {ref} from 'vue'

export default function useTopPageItems() {
  const loading = ref(false)
  const {apiExecute, parseHtmlResponseError} = useApi()
  const state = useSearchResultState()

  const setProductList = (response: any): void => {
    if (response?.items) {
      state.productList.all = response.items.map((item: any) => ({
        ...item,
        bidPrice: item.bid_status?.bid_price?.toString() || '0',
        bidQuantity: item.bid_status?.bid_quantity?.toString() || '1',
      }))
      state.totalCount.value = response.total_count || response.count || 0
    }
  }

  /**
   * Fetch new auction items for top page
   */
  const fetchNewItems = async (): Promise<void> => {
    loading.value = true

    try {
      const params = {
        languageCode: 'ja',
        limit: 10,
      }

      // TODO
      // const response = await apiExecute('public/get-new-auction-items', params)
      const response = null
      setProductList(response)
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * Fetch recommended auction items for top page
   */
  const fetchRecommendedItems = async (): Promise<void> => {
    loading.value = true

    try {
      const params = {
        languageCode: 'ja',
        limit: 10,
      }

      const response = await apiExecute(
        'public/get-recommended-auction-items',
        params
      )
      setProductList(response)
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * Refresh new auction items
   */
  const refreshNewItems = async (): Promise<void> => {
    await fetchNewItems()
  }

  /**
   * Refresh recommended auction items
   */
  const refreshRecommendedItems = async (): Promise<void> => {
    await fetchRecommendedItems()
  }

  return {
    loading,
    fetchNewItems,
    fetchRecommendedItems,
    refreshNewItems,
    refreshRecommendedItems,
  }
}
