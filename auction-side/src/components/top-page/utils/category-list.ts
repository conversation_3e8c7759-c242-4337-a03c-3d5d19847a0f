import {ref} from 'vue'

// TODO: update after receive image from client
// import shoulderBagImage from '@/assets/img/category/top_category01.png'
// import toteBagImage from '@/assets/img/category/top_category01.png'
// import pouchImage from '@/assets/img/category/top_category01.png'
// import ecoBagImage from '@/assets/img/category/top_category01.png'
// import secondBagImage from '@/assets/img/category/top_category01.png'
// import handBagImage from '@/assets/img/category/top_category01.png'
// import basketImage from '@/assets/img/category/top_category01.png'
// import clutchBagImage from '@/assets/img/category/top_category01.png'
// import suitcaseImage from '@/assets/img/category/top_category01.png'
// import vanityBagImage from '@/assets/img/category/top_category01.png'
// import briefcaseImage from '@/assets/img/category/top_category01.png'
// import bostonBagImage from '@/assets/img/category/top_category01.png'
// import bodyBagImage from '@/assets/img/category/top_category01.png'
// import backpackImage from '@/assets/img/category/top_category01.png'
// import keyCaseImage from '@/assets/img/category/top_category01.png'
// import walletImage from '@/assets/img/category/top_category01.png'
// import phoneCaseImage from '@/assets/img/category/top_category01.png'
// import pochetteImage from '@/assets/img/category/top_category01.png'
// import waistBagImage from '@/assets/img/category/top_category01.png'
// import drawstringBagImage from '@/assets/img/category/top_category01.png'
// import mothersBagImage from '@/assets/img/category/top_category01.png'
// import documentCaseImage from '@/assets/img/category/top_category01.png'
// import notebookImage from '@/assets/img/category/top_category01.png'
// import shoesImage from '@/assets/img/category/top_category01.png'
// import accessoryCaseImage from '@/assets/img/category/top_category01.png'

import {
  default as accessoryCaseImage,
  default as backpackImage,
  default as basketImage,
  default as bodyBagImage,
  default as bostonBagImage,
  default as briefcaseImage,
  default as clutchBagImage,
  default as documentCaseImage,
  default as drawstringBagImage,
  default as ecoBagImage,
  default as handBagImage,
  default as keyCaseImage,
  default as mothersBagImage,
  default as notebookImage,
  default as phoneCaseImage,
  default as pochetteImage,
  default as pouchImage,
  default as secondBagImage,
  default as shoesImage,
  default as shoulderBagImage,
  default as suitcaseImage,
  default as toteBagImage,
  default as vanityBagImage,
  default as waistBagImage,
  default as walletImage,
} from '@/assets/img/category/top_category01.png'

export type TopPageCategoryItem = {
  id: string
  name: string
  imagePath: string
  routePath: string
  description?: string
}

export const topPageCategories = ref<TopPageCategoryItem[]>([
  {
    id: 'shoulder-bag',
    name: 'ショルダーバッグ',
    imagePath: shoulderBagImage,
    routePath: '/auction-list?category=shoulder-bag',
    description: 'ショルダーバッグの商品一覧',
  },
  {
    id: 'tote-bag',
    name: 'トートバッグ',
    imagePath: toteBagImage,
    routePath: '/auction-list?category=tote-bag',
    description: 'トートバッグの商品一覧',
  },
  {
    id: 'pouch',
    name: 'ポーチ',
    imagePath: pouchImage,
    routePath: '/auction-list?category=pouch',
    description: 'ポーチの商品一覧',
  },
  {
    id: 'eco-bag',
    name: 'エコバッグ',
    imagePath: ecoBagImage,
    routePath: '/auction-list?category=eco-bag',
    description: 'エコバッグの商品一覧',
  },
  {
    id: 'second-bag',
    name: 'セカンドバッグ',
    imagePath: secondBagImage,
    routePath: '/auction-list?category=second-bag',
    description: 'セカンドバッグの商品一覧',
  },
  {
    id: 'hand-bag',
    name: 'ハンドバッグ',
    imagePath: handBagImage,
    routePath: '/auction-list?category=hand-bag',
    description: 'ハンドバッグの商品一覧',
  },
  {
    id: 'basket',
    name: 'バスケット・かご',
    imagePath: basketImage,
    routePath: '/auction-list?category=basket',
    description: 'バスケット・かごの商品一覧',
  },
  {
    id: 'clutch-bag',
    name: 'クラッチバッグ',
    imagePath: clutchBagImage,
    routePath: '/auction-list?category=clutch-bag',
    description: 'クラッチバッグの商品一覧',
  },
  {
    id: 'suitcase',
    name: 'スーツケース',
    imagePath: suitcaseImage,
    routePath: '/auction-list?category=suitcase',
    description: 'スーツケースの商品一覧',
  },
  {
    id: 'vanity-bag',
    name: 'バニティバッグ',
    imagePath: vanityBagImage,
    routePath: '/auction-list?category=vanity-bag',
    description: 'バニティバッグの商品一覧',
  },
  {
    id: 'briefcase',
    name: 'ブリーフケース',
    imagePath: briefcaseImage,
    routePath: '/auction-list?category=briefcase',
    description: 'ブリーフケースの商品一覧',
  },
  {
    id: 'boston-bag',
    name: 'ボストンバッグ',
    imagePath: bostonBagImage,
    routePath: '/auction-list?category=boston-bag',
    description: 'ボストンバッグの商品一覧',
  },
  {
    id: 'body-bag',
    name: 'ボディバッグ',
    imagePath: bodyBagImage,
    routePath: '/auction-list?category=body-bag',
    description: 'ボディバッグの商品一覧',
  },
  {
    id: 'backpack',
    name: 'リュックサック・デイパック',
    imagePath: backpackImage,
    routePath: '/auction-list?category=backpack',
    description: 'リュックサック・デイパックの商品一覧',
  },
  {
    id: 'key-case',
    name: 'キーケース',
    imagePath: keyCaseImage,
    routePath: '/auction-list?category=key-case',
    description: 'キーケースの商品一覧',
  },
  {
    id: 'wallet',
    name: '財布',
    imagePath: walletImage,
    routePath: '/auction-list?category=wallet',
    description: '財布の商品一覧',
  },
  {
    id: 'phone-case',
    name: '携帯電話ケース',
    imagePath: phoneCaseImage,
    routePath: '/auction-list?category=phone-case',
    description: '携帯電話ケースの商品一覧',
  },
  {
    id: 'pochette',
    name: 'ポシェット',
    imagePath: pochetteImage,
    routePath: '/auction-list?category=pochette',
    description: 'ポシェットの商品一覧',
  },
  {
    id: 'waist-bag',
    name: 'ウエストバッグ',
    imagePath: waistBagImage,
    routePath: '/auction-list?category=waist-bag',
    description: 'ウエストバッグの商品一覧',
  },
  {
    id: 'drawstring-bag',
    name: '巾着',
    imagePath: drawstringBagImage,
    routePath: '/auction-list?category=drawstring-bag',
    description: '巾着の商品一覧',
  },
  {
    id: 'mothers-bag',
    name: 'マザーズバッグ',
    imagePath: mothersBagImage,
    routePath: '/auction-list?category=mothers-bag',
    description: 'マザーズバッグの商品一覧',
  },
  {
    id: 'document-case',
    name: '書類ケース',
    imagePath: documentCaseImage,
    routePath: '/auction-list?category=document-case',
    description: '書類ケースの商品一覧',
  },
  {
    id: 'notebook',
    name: '手帳',
    imagePath: notebookImage,
    routePath: '/auction-list?category=notebook',
    description: '手帳の商品一覧',
  },
  {
    id: 'shoes',
    name: '靴',
    imagePath: shoesImage,
    routePath: '/auction-list?category=shoes',
    description: '靴の商品一覧',
  },
  {
    id: 'accessory-case',
    name: '小物入れ',
    imagePath: accessoryCaseImage,
    routePath: '/auction-list?category=accessory-case',
    description: '小物入れの商品一覧',
  },
])
