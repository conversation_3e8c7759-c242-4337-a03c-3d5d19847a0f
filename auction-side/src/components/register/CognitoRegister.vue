<template>
  <div class="registration-wrapper">
    <div class="container">
      <!-- Success Message Component -->
      <section
        v-if="registrationSuccess"
        id="registration-success"
        class="fade-in"
      >
        <div class="success-message">
          <div class="success-animation">
            <div class="success-icon">
              <div class="check-mark">
                <svg viewBox="0 0 24 24" class="check-svg">
                  <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                </svg>
              </div>
            </div>
          </div>

          <h3 class="success-title">会員登録が完了しました</h3>
          <p class="success-subtitle">以下の情報でアカウントが作成されました</p>

          <div class="user-details">
            <div class="detail-card">
              <div class="detail-item">
                <i class="icon-user"></i>
                <div class="detail-content">
                  <span class="detail-label">会員名</span>
                  <span class="detail-value">{{ createdUser.memberName }}</span>
                </div>
              </div>
              <div class="detail-item">
                <i class="icon-mail"></i>
                <div class="detail-content">
                  <span class="detail-label">メールアドレス</span>
                  <span class="detail-value">{{ createdUser.email }}</span>
                </div>
              </div>
              <div class="detail-item">
                <i class="icon-globe"></i>
                <div class="detail-content">
                  <span class="detail-label">言語設定</span>
                  <span class="detail-value">{{
                    getLanguageName(createdUser.language)
                  }}</span>
                </div>
              </div>
              <div class="detail-item">
                <i class="icon-tag"></i>
                <div class="detail-content">
                  <span class="detail-label">テナントID</span>
                  <span class="detail-value">{{ createdUser.tenantId }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="success-actions">
            <button class="btn-secondary" @click="backToRegister">
              <i class="icon-arrow-left"></i>
              戻る
            </button>
            <button class="btn-primary" @click="navigateToLogin">
              <i class="icon-login"></i>
              ログイン画面へ
            </button>
          </div>
        </div>
      </section>

      <!-- Registration Form -->
      <section v-else id="cognito-register-form" class="slide-in">
        <div class="form-card">
          <div class="form-header">
            <div class="form-icon">
              <i class="icon-user-plus"></i>
            </div>
            <h3 class="form-title">アカウント作成</h3>
            <p class="form-description">
              オークション会員アカウントを作成してください
            </p>
          </div>

          <!-- Error Messages -->
          <div v-if="registerMsg.length > 0" class="error-messages slide-down">
            <div class="error-card">
              <i class="icon-alert"></i>
              <div class="error-content">
                <div
                  v-for="(msg, index) in registerMsg"
                  :key="index"
                  class="err-txt"
                >
                  {{ msg }}
                </div>
              </div>
            </div>
          </div>

          <form
            @submit.prevent="onSubmit"
            class="modern-form"
            autocomplete="off"
          >
            <!-- Chrome autocomplete prevention: Hidden dummy fields -->
            <input type="text" style="display: none" autocomplete="off" />
            <input type="password" style="display: none" autocomplete="off" />
            <!-- 会員名 -->
            <div class="form-group">
              <div class="input-wrapper">
                <label class="floating-label">
                  <i class="icon-user input-icon"></i>
                  <input
                    v-model="formData.memberName"
                    type="text"
                    name="auction_member_display_name"
                    :class="[
                      'modern-input',
                      {
                        error: errors.memberName,
                        valid: formData.memberName && !errors.memberName,
                        focused: focusedField === 'memberName',
                      },
                    ]"
                    placeholder=" "
                    autocomplete="new-password"
                    readonly
                    onfocus="this.removeAttribute('readonly')"
                    @focus="focusedField = 'memberName'"
                    @blur="handleBlur('memberName')"
                    @input="validateField('memberName')"
                  />
                  <span class="label-text">会員名</span>
                  <div
                    v-if="formData.memberName && !errors.memberName"
                    class="validation-check"
                  >
                    <i class="icon-check"></i>
                  </div>
                </label>
                <div class="input-border"></div>
              </div>
              <div v-if="errors.memberName" class="error-message slide-down">
                <i class="icon-alert-circle"></i>
                {{ errors.memberName }}
              </div>
              <div
                v-else-if="!errors.memberName && formData.memberName"
                class="help-text"
              >
                <i class="icon-info"></i>
                2文字以上100文字以下で入力してください
              </div>
            </div>

            <!-- メールアドレス -->
            <div class="form-group">
              <div class="input-wrapper">
                <label class="floating-label">
                  <i class="icon-mail input-icon"></i>
                  <input
                    v-model="formData.email"
                    type="text"
                    name="auction_user_email_address"
                    :class="[
                      'modern-input',
                      {
                        error: errors.email,
                        valid: formData.email && !errors.email,
                        focused: focusedField === 'email',
                      },
                    ]"
                    placeholder=" "
                    autocomplete="new-password"
                    readonly
                    onfocus="this.removeAttribute('readonly')"
                    @focus="focusedField = 'email'"
                    @blur="handleBlur('email')"
                    @input="validateField('email')"
                  />
                  <span class="label-text">メールアドレス</span>
                  <div
                    v-if="formData.email && !errors.email"
                    class="validation-check"
                  >
                    <i class="icon-check"></i>
                  </div>
                </label>
                <div class="input-border"></div>
              </div>
              <div v-if="errors.email" class="error-message slide-down">
                <i class="icon-alert-circle"></i>
                {{ errors.email }}
              </div>
              <div v-else class="help-text">
                <i class="icon-info"></i>
                ログイン時に使用するメールアドレスを入力してください
              </div>
            </div>

            <!-- パスワード -->
            <div class="form-group">
              <div class="input-wrapper">
                <label class="floating-label">
                  <i class="icon-lock input-icon"></i>
                  <input
                    v-model="formData.password"
                    :type="showPassword ? 'text' : 'password'"
                    name="auction_new_user_password"
                    :class="[
                      'modern-input',
                      {
                        error: errors.password,
                        valid: formData.password && !errors.password,
                        focused: focusedField === 'password',
                      },
                    ]"
                    placeholder=" "
                    autocomplete="new-password"
                    readonly
                    onfocus="this.removeAttribute('readonly')"
                    @focus="focusedField = 'password'"
                    @blur="handleBlur('password')"
                    @input="validateField('password')"
                  />
                  <span class="label-text">パスワード</span>
                  <button
                    type="button"
                    class="password-toggle"
                    @click="showPassword = !showPassword"
                  >
                    <i :class="showPassword ? 'icon-eye-off' : 'icon-eye'"></i>
                  </button>
                  <div
                    v-if="formData.password && !errors.password"
                    class="validation-check"
                  >
                    <i class="icon-check"></i>
                  </div>
                </label>
                <div class="input-border"></div>
              </div>

              <!-- Password Strength Indicator -->
              <div v-if="formData.password" class="password-strength">
                <div class="strength-bar">
                  <div
                    class="strength-fill"
                    :class="passwordStrength.class"
                    :style="{width: passwordStrength.width}"
                  ></div>
                </div>
                <span class="strength-text" :class="passwordStrength.class">
                  {{ passwordStrength.text }}
                </span>
              </div>

              <div v-if="errors.password" class="error-message slide-down">
                <i class="icon-alert-circle"></i>
                {{ errors.password }}
              </div>
              <div v-else class="help-text">
                <i class="icon-info"></i>
                8文字以上で大文字・小文字・数字を含む
              </div>
            </div>

            <!-- パスワード（確認用） -->
            <div class="form-group">
              <div class="input-wrapper">
                <label class="floating-label">
                  <i class="icon-lock input-icon"></i>
                  <input
                    v-model="formData.passwordConfirm"
                    type="password"
                    name="auction_confirm_user_password"
                    :class="[
                      'modern-input',
                      {
                        error: errors.passwordConfirm,
                        valid:
                          formData.passwordConfirm && !errors.passwordConfirm,
                        focused: focusedField === 'passwordConfirm',
                      },
                    ]"
                    placeholder=" "
                    autocomplete="new-password"
                    readonly
                    onfocus="this.removeAttribute('readonly')"
                    @focus="focusedField = 'passwordConfirm'"
                    @blur="handleBlur('passwordConfirm')"
                    @input="validateField('passwordConfirm')"
                  />
                  <span class="label-text">パスワード（確認用）</span>
                  <div
                    v-if="formData.passwordConfirm && !errors.passwordConfirm"
                    class="validation-check"
                  >
                    <i class="icon-check"></i>
                  </div>
                </label>
                <div class="input-border"></div>
              </div>
              <div
                v-if="errors.passwordConfirm"
                class="error-message slide-down"
              >
                <i class="icon-alert-circle"></i>
                {{ errors.passwordConfirm }}
              </div>
            </div>

            <!-- 言語設定 -->
            <div class="form-group">
              <div class="select-wrapper">
                <label class="floating-label">
                  <i class="icon-globe input-icon"></i>
                  <select
                    v-model="formData.language"
                    name="auction_user_language_preference"
                    :class="[
                      'modern-select',
                      {
                        error: errors.language,
                        valid: formData.language && !errors.language,
                        focused: focusedField === 'language',
                      },
                    ]"
                    autocomplete="new-password"
                    @focus="focusedField = 'language'"
                    @blur="handleBlur('language')"
                    @change="validateField('language')"
                  >
                    <option value="">選択してください</option>
                    <option
                      v-for="opt in languageOptions"
                      :key="opt.value"
                      :value="opt.value"
                    >
                      {{ opt.label }}
                    </option>
                  </select>
                  <span class="label-text">言語設定</span>
                  <div
                    v-if="formData.language && !errors.language"
                    class="validation-check"
                  >
                    <i class="icon-check"></i>
                  </div>
                </label>
                <div class="input-border"></div>
              </div>
              <div v-if="errors.language" class="error-message slide-down">
                <i class="icon-alert-circle"></i>
                {{ errors.language }}
              </div>
            </div>

            <!-- 登録ボタン -->
            <div class="submit-section">
              <button
                type="submit"
                :disabled="loading || !isFormValid"
                class="btn-register modern-btn"
              >
                <div v-if="loading" class="loading-spinner">
                  <div class="spinner"></div>
                  <span>登録中...</span>
                </div>
                <div v-else class="btn-content">
                  <i class="icon-user-plus"></i>
                  <span>アカウント作成</span>
                </div>
              </button>

              <!-- Progress indicator -->
              <div class="form-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :style="{width: formProgress + '%'}"
                  ></div>
                </div>
                <span class="progress-text"
                  >{{ Math.round(formProgress) }}% 完了</span
                >
              </div>
            </div>

            <!-- ログインリンク -->
            <div class="login-link">
              <p class="link-text">既にアカウントをお持ちですか？</p>
              <a @click="navigateToLogin" class="register-btt">
                <i class="icon-login"></i>
                ログイン画面へ
              </a>
            </div>
          </form>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
  defineOptions({name: 'CognitoRegisterPage'})
  import {computed, nextTick, onMounted, reactive, ref} from 'vue'
  import {useRouter} from 'vue-router'
  import useApi from '../../composables/useApi'
  import {useCognitoAuthStore} from '../../stores/cognitoAuth'

  const router = useRouter()
  const cognitoAuth = useCognitoAuthStore()
  const {apiExecute} = useApi()

  const registerMsg = ref([])
  const loading = ref(false)
  const registrationSuccess = ref(false)
  const focusedField = ref('')
  const showPassword = ref(false)
  const createdUser = ref({
    memberName: '',
    email: '',
    language: '',
    tenantId: '',
  })

  // Form data
  const formData = reactive({
    memberName: '',
    email: '',
    password: '',
    passwordConfirm: '',
    language: 'ja', // Default to Japanese
  })

  // Form errors
  const errors = reactive({
    memberName: '',
    email: '',
    password: '',
    passwordConfirm: '',
    language: '',
  })

  // Language options for auction users
  const languageOptions = [
    {value: 'ja', label: '日本語'},
    {value: 'en', label: 'English'},
  ]

  // Helper function to get language name
  const getLanguageName = langCode => {
    const lang = languageOptions.find(l => l.value === langCode)
    return lang ? lang.label : langCode
  }

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  // Password validation regex - requires uppercase, lowercase, and number (no special characters)
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]/

  // Validation functions
  const validateMemberName = value => {
    if (!value) return '会員名を入力してください'
    if (value.length < 2) return '会員名は2文字以上で入力してください'
    if (value.length > 100) return '会員名は100文字以下で入力してください'
    return ''
  }

  const validateEmail = value => {
    if (!value) return 'メールアドレスを入力してください'
    if (!emailRegex.test(value)) return '有効なメールアドレスを入力してください'
    return ''
  }

  const validatePassword = value => {
    if (!value) return 'パスワードを入力してください'
    if (value.length < 8) return 'パスワードは8文字以上で入力してください'
    if (value.length > 50) return 'パスワードは50文字以下で入力してください'
    if (!passwordRegex.test(value)) {
      return 'パスワードは大文字、小文字、数字を含む必要があります'
    }
    return ''
  }

  const validatePasswordConfirm = value => {
    if (!value) return 'パスワード確認を入力してください'
    if (value !== formData.password) return 'パスワードが一致しません'
    return ''
  }

  const validateLanguage = value => {
    if (!value) return '言語設定を選択してください'
    return ''
  }

  // Validate individual field
  const validateField = fieldName => {
    const value = formData[fieldName]
    switch (fieldName) {
      case 'memberName':
        errors.memberName = validateMemberName(value)
        break
      case 'email':
        errors.email = validateEmail(value)
        break
      case 'password':
        errors.password = validatePassword(value)
        // Re-validate password confirmation if it exists
        if (formData.passwordConfirm) {
          errors.passwordConfirm = validatePasswordConfirm(
            formData.passwordConfirm
          )
        }
        break
      case 'passwordConfirm':
        errors.passwordConfirm = validatePasswordConfirm(value)
        break
      case 'language':
        errors.language = validateLanguage(value)
        break
    }
  }

  // Handle field blur with animation
  const handleBlur = fieldName => {
    focusedField.value = ''
    validateField(fieldName)
  }

  // Check if form is valid
  const isFormValid = computed(() => {
    return (
      formData.memberName &&
      formData.email &&
      formData.password &&
      formData.passwordConfirm &&
      formData.language &&
      !errors.memberName &&
      !errors.email &&
      !errors.password &&
      !errors.passwordConfirm &&
      !errors.language
    )
  })

  // Password strength indicator
  const passwordStrength = computed(() => {
    const password = formData.password
    if (!password) return {width: '0%', class: '', text: ''}

    let score = 0
    let feedback = []

    // Length check
    if (password.length >= 8) score += 1
    else feedback.push('8文字以上')

    // Uppercase check
    if (/[A-Z]/.test(password)) score += 1
    else feedback.push('大文字')

    // Lowercase check
    if (/[a-z]/.test(password)) score += 1
    else feedback.push('小文字')

    // Number check
    if (/\d/.test(password)) score += 1
    else feedback.push('数字')

    const strengthLevels = [
      {width: '25%', class: 'very-weak', text: '非常に弱い'},
      {width: '50%', class: 'weak', text: '弱い'},
      {width: '75%', class: 'good', text: '良い'},
      {width: '100%', class: 'strong', text: '強い'},
    ]

    return strengthLevels[score] || strengthLevels[0]
  })

  // Form completion progress
  const formProgress = computed(() => {
    const fields = [
      'memberName',
      'email',
      'password',
      'passwordConfirm',
      'language',
    ]
    const completedFields = fields.filter(field => {
      return formData[field] && !errors[field]
    }).length

    return (completedFields / fields.length) * 100
  })

  // Register function
  const register = async () => {
    registerMsg.value = []
    loading.value = true

    try {
      const apiEndpoint = `${import.meta.env.VITE_API_ENDPOINT}cognito-register-member`
      const response = await apiExecute(apiEndpoint, {
        registerData: {
          memberName: formData.memberName,
          email: formData.email,
          password: formData.password,
          passwordConfirm: formData.passwordConfirm,
          language: formData.language,
        },
        languageCode: formData.language,
      })

      console.log(
        '%c 🇧🇭: Registration response ',
        'font-size:16px;background-color:#126d6d;color:white;',
        response
      )

      if (response) {
        createdUser.value = {
          memberName: formData.memberName,
          email: formData.email,
          language: formData.language,
          tenantId: response.tenantId || '1', // Default tenant
        }
        registrationSuccess.value = true
      }
      loading.value = false
    } catch (error) {
      console.error('Registration error:', error)
      loading.value = false

      // Handle specific Cognito errors
      if (error.response?.data) {
        const errorData = error.response.data
        if (errorData.name === 'Email Already Exists') {
          registerMsg.value = ['このメールアドレスは既に登録されています']
        } else if (errorData.name === 'Password Mismatch') {
          registerMsg.value = ['パスワードと確認用パスワードが一致しません']
        } else if (errorData.name === 'Registration Error') {
          registerMsg.value = [errorData.message || '登録に失敗しました']
        } else {
          registerMsg.value = [
            errorData.message || '登録中にエラーが発生しました',
          ]
        }
      } else {
        registerMsg.value = [
          'ネットワークエラーが発生しました。もう一度お試しください。',
        ]
      }
    }
  }

  const onSubmit = () => {
    // Validate all fields before submission
    Object.keys(formData).forEach(fieldName => {
      validateField(fieldName)
    })

    // Only submit if form is valid
    if (isFormValid.value) {
      register()
    }
  }

  const backToRegister = () => {
    registrationSuccess.value = false
    // Reset form data
    Object.keys(formData).forEach(key => {
      formData[key] = key === 'language' ? 'ja' : ''
    })
    // Reset errors
    Object.keys(errors).forEach(key => {
      errors[key] = ''
    })
  }

  const navigateToLogin = () => {
    router.push('/login')
  }

  // Additional autocomplete prevention on component mount
  onMounted(async () => {
    await nextTick()

    // Force clear all form fields after mount to prevent Chrome autocomplete
    const formInputs = document.querySelectorAll('input, select')
    formInputs.forEach(input => {
      if (input.type !== 'submit' && input.type !== 'button') {
        input.value = ''
        input.setAttribute('autocomplete', 'new-password')

        // Additional Chrome-specific prevention with delay
        setTimeout(() => {
          input.value = ''
        }, 100)

        // Another attempt after longer delay for persistent Chrome behavior
        setTimeout(() => {
          input.value = ''
        }, 500)
      }
    })

    // Reset Vue form data to ensure clean state
    Object.keys(formData).forEach(key => {
      if (key === 'language') {
        formData[key] = 'ja' // Default language
      } else {
        formData[key] = ''
      }
    })

    // Clear any errors
    Object.keys(errors).forEach(key => {
      errors[key] = ''
    })
  })
</script>

<style scoped>
  /* CSS Icons - Simple icon font using CSS */
  .icon-user-plus::before {
    content: '👤➕';
  }
  .icon-user::before {
    content: '👤';
  }
  .icon-mail::before {
    content: '✉️';
  }
  .icon-globe::before {
    content: '🌐';
  }
  .icon-tag::before {
    content: '🏷️';
  }
  .icon-arrow-left::before {
    content: '←';
  }
  .icon-login::before {
    content: '🔑';
  }
  .icon-alert::before {
    content: '⚠️';
  }
  .icon-check::before {
    content: '✓';
  }
  .icon-alert-circle::before {
    content: '⚠️';
  }
  .icon-info::before {
    content: 'ℹ️';
  }
  .icon-lock::before {
    content: '🔒';
  }
  .icon-eye::before {
    content: '👁️';
  }
  .icon-eye-off::before {
    content: '🙈';
  }

  /* Background and Layout */
  .registration-wrapper {
    position: relative;
    min-height: 100vh;
    background: linear-gradient(135deg, #fafbfc 0%, #f1f3f6 100%);
  }

  .background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        circle at 25% 25%,
        rgba(66, 127, 174, 0.03) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 75% 75%,
        rgba(66, 127, 174, 0.02) 0%,
        transparent 50%
      );
    pointer-events: none;
  }

  .page-ttl {
    position: relative;
    z-index: 1;
    text-align: center;
    padding: 2rem 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(66, 127, 174, 0.1);
  }

  .page-ttl .ttl {
    font-size: 2.5rem;
    font-weight: 700;
    color: #427fae;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .page-ttl .sub {
    font-size: 1rem;
    color: #666;
    margin: 0.5rem 0 0 0;
    font-weight: 400;
  }

  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes ripple {
    0% {
      transform: scale(0);
      opacity: 1;
    }
    100% {
      transform: scale(4);
      opacity: 0;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .fade-in {
    animation: fadeIn 0.6s ease-out;
  }
  .slide-in {
    animation: slideIn 0.5s ease-out;
  }
  .slide-down {
    animation: slideDown 0.3s ease-out;
  }

  /* Success Message Styles */
  #registration-success {
    position: relative;
    z-index: 1;
    margin: 2rem auto;
    max-width: 700px;
    padding: 0 1rem;
  }

  .success-message {
    background: linear-gradient(135deg, #ffffff 0%, #f8fff9 100%);
    border: 2px solid #28a745;
    border-radius: 20px;
    padding: 3rem 2rem;
    text-align: center;
    box-shadow: 0 20px 40px rgba(40, 167, 69, 0.1);
    position: relative;
    overflow: hidden;
  }

  .success-animation {
    position: relative;
    margin-bottom: 2rem;
  }

  .success-icon {
    position: relative;
    z-index: 2;
  }

  .check-mark {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 50%;
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
    animation: pulse 2s infinite;
  }

  .check-svg {
    width: 40px;
    height: 40px;
    fill: white;
  }

  .success-title {
    font-size: 2rem;
    font-weight: 700;
    color: #427fae;
    margin-bottom: 1rem;
  }

  .success-subtitle {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
  }

  .detail-card {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    padding: 1.5rem;
    margin: 2rem 0;
    backdrop-filter: blur(10px);
  }

  .detail-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(66, 127, 174, 0.1);
  }

  .detail-item:last-child {
    border-bottom: none;
  }

  .detail-item i {
    font-size: 1.5rem;
    margin-right: 1rem;
    color: #427fae;
    width: 2rem;
    text-align: center;
  }

  .detail-content {
    flex: 1;
    text-align: left;
  }

  .detail-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.25rem;
  }

  .detail-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
  }

  .success-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
  }

  .btn-secondary,
  .btn-primary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
  }

  .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
  }

  .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
  }

  .btn-primary {
    background: linear-gradient(135deg, #427fae 0%, #356a94 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(66, 127, 174, 0.3);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 127, 174, 0.4);
  }

  /* Form Styles */
  #cognito-register-form {
    position: relative;
    z-index: 1;
    margin: 2rem auto;
    max-width: 600px;
    padding: 0 1rem;
  }

  .form-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 2.5rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .form-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .form-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #427fae 0%, #356a94 100%);
    border-radius: 50%;
    margin-bottom: 1rem;
    box-shadow: 0 10px 30px rgba(66, 127, 174, 0.3);
  }

  .form-icon i {
    font-size: 1.5rem;
    color: white;
  }

  .form-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #427fae;
    margin-bottom: 0.5rem;
  }

  .form-description {
    font-size: 1rem;
    color: #666;
    margin: 0;
  }

  /* Error Messages */
  .error-messages {
    margin-bottom: 1.5rem;
  }

  .error-card {
    background: rgba(239, 68, 68, 0.06);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 10px;
    padding: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .error-card i {
    font-size: 1.1rem;
    color: #dc2626;
    opacity: 0.8;
  }

  .error-content .err-txt {
    color: #dc2626;
    font-weight: 400;
    margin: 0.25rem 0;
    font-size: 0.9rem;
  }

  /* Modern Form */
  .modern-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-group {
    position: relative;
  }

  .input-wrapper,
  .select-wrapper {
    position: relative;
  }

  .floating-label {
    position: relative;
    display: block;
  }

  .input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.25rem;
    color: #999;
    z-index: 2;
    transition: color 0.3s ease;
  }

  .modern-input,
  .modern-select {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    outline: none;
  }

  .modern-input:focus,
  .modern-select:focus {
    border-color: #427fae;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 3px rgba(66, 127, 174, 0.1);
  }

  .modern-input.focused,
  .modern-select.focused {
    border-color: #427fae;
    background: rgba(255, 255, 255, 1);
  }

  .modern-input.valid,
  .modern-select.valid {
    border-color: #28a745;
    background: rgba(248, 255, 249, 0.9);
  }

  .modern-input.error,
  .modern-select.error {
    border-color: #dc3545;
    background: rgba(255, 248, 248, 0.9);
  }

  .label-text {
    position: absolute;
    left: 3rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1rem;
    color: #666;
    pointer-events: none;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    padding: 0 0.5rem;
  }

  .modern-input:focus + .label-text,
  .modern-input:not(:placeholder-shown) + .label-text,
  .modern-select:focus + .label-text,
  .modern-select:not([value='']) + .label-text {
    top: 0;
    left: 2.5rem;
    font-size: 0.75rem;
    color: #427fae;
    font-weight: 600;
  }

  .validation-check {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
  }

  .validation-check i {
    font-size: 0.875rem;
    color: white;
  }

  .input-border {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #427fae 0%, #356a94 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  .modern-input:focus ~ .input-border,
  .modern-select:focus ~ .input-border {
    transform: scaleX(1);
  }

  /* Password Toggle */
  .password-toggle {
    position: absolute;
    right: 3rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.3s ease;
    z-index: 2;
  }

  .password-toggle:hover {
    background: rgba(66, 127, 174, 0.1);
  }

  .password-toggle i {
    font-size: 1rem;
    color: #666;
  }

  /* Password Strength */
  .password-strength {
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .strength-bar {
    flex: 1;
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
  }

  .strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
  }

  .strength-fill.very-weak {
    background: #dc3545;
  }
  .strength-fill.weak {
    background: #fd7e14;
  }
  .strength-fill.fair {
    background: #ffc107;
  }
  .strength-fill.good {
    background: #20c997;
  }
  .strength-fill.strong {
    background: #28a745;
  }

  .strength-text {
    font-size: 0.875rem;
    font-weight: 600;
  }

  .strength-text.very-weak {
    color: #dc3545;
  }
  .strength-text.weak {
    color: #fd7e14;
  }
  .strength-text.fair {
    color: #ffc107;
  }
  .strength-text.good {
    color: #20c997;
  }
  .strength-text.strong {
    color: #28a745;
  }

  /* Help Text and Error Messages */
  .error-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: rgba(239, 68, 68, 0.05);
    border: 1px solid rgba(239, 68, 68, 0.15);
    border-radius: 6px;
    font-size: 0.8rem;
    color: #dc2626;
    font-weight: 400;
  }

  .error-message i {
    font-size: 0.875rem;
    color: #dc2626;
    opacity: 0.7;
  }

  .help-text {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    color: #666;
    background: rgba(66, 127, 174, 0.05);
    border-radius: 6px;
  }

  .help-text i {
    font-size: 0.875rem;
    color: #427fae;
  }

  /* Submit Section */
  .submit-section {
    margin-top: 2rem;
    text-align: center;
  }

  .btn-register.modern-btn {
    width: 100%;
    padding: 1.25rem 2rem;
    background: linear-gradient(135deg, #427fae 0%, #356a94 100%);
    color: white;
    border: none;
    border-radius: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(66, 127, 174, 0.3);
    position: relative;
    overflow: hidden;
  }

  .btn-register.modern-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(66, 127, 174, 0.4);
  }

  .btn-register.modern-btn:disabled {
    background: linear-gradient(135deg, #ccc 0%, #999 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
  }

  .loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
  }

  .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  /* Form Progress */
  .form-progress {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .progress-bar {
    flex: 1;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #427fae 0%, #356a94 100%);
    border-radius: 3px;
    transition: width 0.5s ease;
  }

  .progress-text {
    font-size: 0.875rem;
    color: #666;
    font-weight: 600;
    min-width: 60px;
  }

  /* Login Link */
  .login-link {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(66, 127, 174, 0.1);
  }

  .link-text {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.75rem;
  }

  .register-btt {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #427fae;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .register-btt:hover {
    background: rgba(66, 127, 174, 0.1);
    text-decoration: none;
    transform: translateY(-1px);
  }

  /* Mobile Responsive */
  @media screen and (max-width: 767px) {
    .registration-wrapper {
      min-height: auto;
    }

    .page-ttl {
      padding: 1.5rem 0;
    }

    .page-ttl .ttl {
      font-size: 2rem;
    }

    #registration-success,
    #cognito-register-form {
      margin: 1rem;
      max-width: none;
    }

    .form-card {
      padding: 1.5rem;
      border-radius: 20px;
    }

    .success-message {
      padding: 2rem 1.5rem;
      border-radius: 15px;
    }

    .success-title {
      font-size: 1.5rem;
    }

    .form-title {
      font-size: 1.5rem;
    }

    .success-actions {
      flex-direction: column;
      gap: 0.75rem;
    }

    .btn-secondary,
    .btn-primary {
      width: 100%;
      justify-content: center;
    }

    .modern-input,
    .modern-select {
      padding: 0.875rem 0.875rem 0.875rem 2.5rem;
      font-size: 0.9rem;
    }

    .input-icon {
      left: 0.75rem;
      font-size: 1rem;
    }

    .label-text {
      left: 2.5rem;
      font-size: 0.9rem;
    }

    .modern-input:focus + .label-text,
    .modern-input:not(:placeholder-shown) + .label-text,
    .modern-select:focus + .label-text,
    .modern-select:not([value='']) + .label-text {
      left: 2rem;
      font-size: 0.7rem;
    }

    .password-toggle {
      right: 2.5rem;
    }

    .validation-check {
      right: 0.75rem;
      width: 20px;
      height: 20px;
    }

    .btn-register.modern-btn {
      padding: 1rem 1.5rem;
      font-size: 1rem;
    }

    .form-progress {
      flex-direction: column;
      gap: 0.5rem;
      text-align: center;
    }

    .progress-text {
      min-width: auto;
    }

    .detail-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
      padding: 0.75rem 0;
    }

    .detail-item i {
      margin-right: 0;
      margin-bottom: 0.25rem;
    }

    .detail-content {
      width: 100%;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .modern-input,
    .modern-select {
      border-width: 3px;
    }

    .btn-register.modern-btn {
      border: 2px solid #427fae;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
</style>
