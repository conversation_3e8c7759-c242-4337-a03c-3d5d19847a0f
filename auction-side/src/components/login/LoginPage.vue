<script setup lang="ts">
  import {computed, onMounted, reactive, ref} from 'vue'
  import {
    useRoute,
    useRouter,
    type RouteLocationNormalizedLoaded,
    type Router,
  } from 'vue-router'
  import {PATH_NAME} from '../../defined/const.js'
  import {useCognitoAuthStore} from '../../stores/cognitoAuth.js'

  // Router and route
  const route: RouteLocationNormalizedLoaded = useRoute()
  const router: Router = useRouter()
  const cognitoAuth = useCognitoAuthStore()

  // Form data
  const formData = reactive({
    email: '',
    password: '',
    rememberLogin: false,
    agreeToTerms: false,
  })

  // Form state
  const loading = ref(false)
  const loginErrors = ref<string[]>([])
  const showErrors = ref(false)

  // Form validation
  const formErrors = reactive({
    email: '',
    password: '',
    agreeToTerms: '',
  })

  // Validation functions
  const validateEmail = (email: string): string => {
    if (!email) return '未入力です'
    return ''
  }

  const validatePassword = (password: string): string => {
    if (!password) return '未入力です'
    if (password.length < 8 || password.length > 14)
      return 'パスワードは8～14文字で入力してください'
    return ''
  }

  const validateAgreeToTerms = (agreed: boolean): string => {
    if (!agreed) return '参加規約に同意してください'
    return ''
  }

  // Validate individual field
  const validateField = (fieldName: keyof typeof formData) => {
    switch (fieldName) {
      case 'email':
        formErrors.email = validateEmail(formData.email)
        break
      case 'password':
        formErrors.password = validatePassword(formData.password)
        break
      case 'agreeToTerms':
        formErrors.agreeToTerms = validateAgreeToTerms(formData.agreeToTerms)
        break
    }
  }

  // Check if form is valid
  const isFormValid = computed(() => {
    return (
      formData.email &&
      formData.password &&
      formData.agreeToTerms &&
      !formErrors.email &&
      !formErrors.password &&
      !formErrors.agreeToTerms
    )
  })

  // Handle login
  const handleLogin = async () => {
    // Validate all fields
    validateField('email')
    validateField('password')
    validateField('agreeToTerms')

    // Show errors if validation fails
    if (!isFormValid.value) {
      showErrors.value = true
      return
    }

    loading.value = true
    loginErrors.value = []
    showErrors.value = false

    try {
      const result = await cognitoAuth.login(formData.email, formData.password)

      if (result.type === 'SUCCESS') {
        // Successful login - redirect to intended page or top
        const redirectPath =
          (route.query.redirect as string) ||
          route.redirectedFrom?.path ||
          PATH_NAME.TOP
        router.replace(redirectPath)
      } else if (result.type === 'NEW_PASSWORD_REQUIRED') {
        // Handle new password requirement
        loginErrors.value = [
          result.message || '初回ログイン時はパスワードの変更が必要です。',
        ]
        showErrors.value = true
      } else {
        // Handle other error types
        loginErrors.value = [result.message || 'ログインに失敗しました。']
        showErrors.value = true
      }
    } catch (error: any) {
      console.error('Login error:', error)

      // Handle specific Cognito errors
      if (error.message) {
        if (error.message.includes('User is disabled')) {
          loginErrors.value = [
            'このアカウントは無効化されています。管理者にお問い合わせください。',
          ]
        } else if (error.message.includes('NotAuthorizedException')) {
          loginErrors.value = ['ログインIDまたはパスワードが正しくありません。']
        } else {
          loginErrors.value = [error.message]
        }
      } else {
        loginErrors.value = [
          'ログインに失敗しました。入力情報を確認してください。',
        ]
      }
      showErrors.value = true
    } finally {
      loading.value = false
    }
  }

  const navigateToReminder = () => {
    router.push(PATH_NAME.REMINDER)
  }

  // Hide errors on component mount for clean initial state
  onMounted(() => {
    showErrors.value = false
    loginErrors.value = []
  })
</script>

<template>
  <h2 class="page-ttl">
    <p class="ttl">ログイン</p>
    <p class="sub">login</p>
  </h2>
  <div class="container">
    <section id="login-form">
      <form @submit.prevent="handleLogin">
        <!-- Global Error Messages -->
        <div v-if="showErrors && loginErrors.length > 0" class="id-pass-err">
          <span
            v-for="(error, index) in loginErrors"
            :key="index"
            class="err-txt"
          >
            {{ error }}
          </span>
        </div>

        <table class="tbl-login">
          <tbody>
            <tr>
              <th>ログインID<em class="req">※必須</em></th>
              <td>
                <input
                  v-model="formData.email"
                  type="text"
                  :class="[
                    'ime-dis',
                    {
                      err: showErrors && formErrors.email,
                    },
                  ]"
                  placeholder="8～14文字の半角英数字"
                  autocomplete="username"
                  @blur="validateField('email')"
                  @input="formErrors.email = ''"
                  required
                />
                <p v-if="showErrors && formErrors.email" class="err-txt">
                  {{ formErrors.email }}
                </p>
              </td>
            </tr>
            <tr>
              <th>パスワード<em class="req">※必須</em></th>
              <td>
                <input
                  v-model="formData.password"
                  type="password"
                  :class="[
                    'ime-dis',
                    {
                      err: showErrors && formErrors.password,
                    },
                  ]"
                  placeholder="8～14文字の半角英数字"
                  autocomplete="current-password"
                  @blur="validateField('password')"
                  @input="formErrors.password = ''"
                  required
                />
                <p v-if="showErrors && formErrors.password" class="err-txt">
                  {{ formErrors.password }}
                </p>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="check-idpass">
          <label>
            <input
              v-model="formData.rememberLogin"
              type="checkbox"
              class="checkbox-input"
            />
            <span class="checkbox-parts">ID・パスワードを保存</span>
          </label>
        </div>
        <div class="forget-pass">
          <a @click="navigateToReminder">パスワードを忘れた方はコチラ</a>
        </div>

        <div class="rule">
          <p class="tit-rule">入札会参加要項</p>
          <embed
            src="/pdf/sample.pdf"
            type="application/pdf"
            width="100%"
            height="150"
          />
          <div class="rule-check">
            <label for="rule-chk">
              <input
                v-model="formData.agreeToTerms"
                type="checkbox"
                id="rule-chk"
                :class="[
                  'checkbox-input',
                  {
                    err: showErrors && formErrors.agreeToTerms,
                  },
                ]"
                @change="validateField('agreeToTerms')"
                required
              />
              <span class="checkbox-parts">参加規約に同意する</span>
            </label>
            <p v-if="showErrors && formErrors.agreeToTerms" class="err-txt">
              {{ formErrors.agreeToTerms }}
            </p>
          </div>
        </div>
        <div class="btn-form">
          <input
            type="submit"
            id="sbm-login"
            :disabled="loading || !isFormValid"
            :value="loading ? '処理中...' : 'ログイン'"
            :class="{loading: loading}"
          />
        </div>
      </form>
      <div class="request">
        <a class="register-btt" @click="() => router.push(PATH_NAME.REGISTER)"
          >新規会員登録</a
        >
        <span class="mx-2">|</span>
        <a
          class="register-btt"
          @click="() => router.push(PATH_NAME.COGNITO_REGISTER)"
          >簡単登録（Cognito）</a
        >
        <p>※商品の価格を見るには会員登録が必要です。</p>
      </div>
    </section>
  </div>
</template>

<style lang="css">
  .register-btt {
    cursor: pointer;
  }

  /* Enhanced error styling */
  /* .id-pass-err {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(239, 68, 68, 0.06);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 8px;
  }

  .err-txt {
    color: #dc2626;
    font-size: 0.875rem;
    font-weight: 400;
    margin: 0.25rem 0;
    display: block;
  } */

  /* Input error state */
  /* .ime-dis.err {
    border-color: #dc3545;
    background: rgba(255, 248, 248, 0.9);
  } */

  /* Checkbox error state */
  /* .checkbox-input.err + .checkbox-parts {
    color: #dc2626;
  } */
  #sbm-login {
    border-radius: 40px !important;
  }

  /* Loading state for submit button */
  #sbm-login.loading {
    opacity: 0.7;
    cursor: not-allowed;
    background: #ccc;
  }

  #sbm-login:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #ccc;
  }

  /* Form validation feedback */
  .ime-dis:focus {
    outline: none;
    border-color: #427fae;
    box-shadow: 0 0 0 2px rgba(66, 127, 174, 0.2);
  }

  .ime-dis.err:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
  }
</style>
